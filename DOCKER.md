# Docker Setup for Snapback License System

This document provides instructions for building and running Docker containers for the Snapback License System monorepo.

## Overview

The project includes Docker configurations for three applications:
- **Web App** (`apps/web`): Vite + React frontend
- **Server App** (`apps/server`): Bun + <PERSON>o backend API
- **Native App** (`apps/native`): Expo app (web build)

All Dockerfiles follow Turborepo best practices using `turbo prune` for optimal caching and smaller image sizes.

## Prerequisites

- Docker installed on your system
- Docker Compose (for the database)

## Building Images

All Docker builds should be run from the **root directory** of the monorepo.

### Web App

```bash
# Build the web app image
docker build -f apps/web/Dockerfile -t snapback-web .

# Run the web app container
docker run -p 3001:3000 snapback-web
```

The web app will be available at `http://localhost:3001`

### Server App

```bash
# Build the server app image
docker build -f apps/server/Dockerfile -t snapback-server .

# Run the server app container
docker run -p 3000:3000 snapback-server
```

The server API will be available at `http://localhost:3000`

### Native App (Web Build)

```bash
# Build the native app image (web version)
docker build -f apps/native/Dockerfile -t snapback-native .

# Run the native app container
docker run -p 3002:3000 snapback-native
```

The native web app will be available at `http://localhost:3002`

## Environment Variables

### Server App

Create a `.env` file in `apps/server/` with the following variables:

```env
DATABASE_URL="postgresql://postgres:password@localhost:5432/snapback-license-system"
BETTER_AUTH_SECRET="your-secret-key-here"
BETTER_AUTH_URL="http://localhost:3000"
```

To pass environment variables to the Docker container:

```bash
docker run -p 3000:3000 --env-file apps/server/.env snapback-server
```

## Database Setup

The server app requires a PostgreSQL database. Use the provided docker-compose.yml:

```bash
# Start the database
cd apps/server
docker compose up -d

# Run database migrations (if needed)
bun run db:migrate
```

## Docker Compose Setup

Create a `docker-compose.yml` in the root directory for running all services:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: snapback-postgres
    environment:
      POSTGRES_DB: snapback-license-system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  server:
    build:
      context: .
      dockerfile: apps/server/Dockerfile
    container_name: snapback-server
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: "********************************************/snapback-license-system"
      BETTER_AUTH_SECRET: "your-secret-key-here"
      BETTER_AUTH_URL: "http://localhost:3000"
    depends_on:
      - postgres

  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
    container_name: snapback-web
    ports:
      - "3001:3000"

  native:
    build:
      context: .
      dockerfile: apps/native/Dockerfile
    container_name: snapback-native
    ports:
      - "3002:3000"

volumes:
  postgres_data:
```

Run all services:

```bash
docker compose up -d
```

## Remote Caching (Optional)

To enable Turborepo remote caching during Docker builds, add build arguments:

```bash
docker build -f apps/web/Dockerfile \
  --build-arg TURBO_TEAM="your-team-name" \
  --build-arg TURBO_TOKEN="your-token" \
  -t snapback-web .
```

## Troubleshooting

### Build Issues

1. **Turbo not found**: Ensure you're building from the root directory
2. **Permission errors**: Check that the non-root user has proper permissions
3. **Out of memory**: Increase Docker's memory allocation

### Runtime Issues

1. **Database connection**: Ensure PostgreSQL is running and accessible
2. **Port conflicts**: Check that ports 3000-3002 are available
3. **Environment variables**: Verify all required env vars are set

## Development vs Production

These Dockerfiles are optimized for production use. For development:

1. Use the regular `bun dev`, `yarn dev` commands
2. Mount volumes for hot reloading
3. Use development environment variables

## Image Optimization

The Dockerfiles use multi-stage builds to minimize image size:

- **Builder stage**: Installs all dependencies and builds the app
- **Runner stage**: Only includes production dependencies and built assets

This results in smaller, more secure production images.
