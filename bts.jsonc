// Better-T-Stack configuration file
// safe to delete

{
  "$schema": "https://r2.better-t-stack.dev/schema.json",
  "version": "2.45.3",
  "createdAt": "2025-09-14T17:24:54.160Z",
  "database": "postgres",
  "orm": "prisma",
  "backend": "hono",
  "runtime": "bun",
  "frontend": [
    "tanstack-router",
    "native-nativewind"
  ],
  "addons": [
    "biome",
    "husky",
    "turborepo"
  ],
  "examples": [],
  "auth": "better-auth",
  "packageManager": "bun",
  "dbSetup": "docker",
  "api": "orpc",
  "webDeploy": "none",
  "serverDeploy": "none"
}