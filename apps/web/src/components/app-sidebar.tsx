import {
	IconCamera,
	IconCreditCard,
	IconDashboard,
	IconDeviceDesktop,
	IconFileAi,
	IconFileDescription,
	IconFileWord,
	IconHelp,
	IconInnerShadowTop,
	IconLicense,
	IconMail,
	IconSearch,
	IconSettings,
	IconTimelineEvent,
	IconUsers,
} from "@tabler/icons-react";
import type * as React from "react";

import { NavDocuments } from "@/components/nav-documents";
import { NavMain } from "@/components/nav-main";
import { NavSecondary } from "@/components/nav-secondary";
import { NavUser } from "@/components/nav-user";
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
} from "@/components/ui/sidebar";

const data = {
	navMain: [
		{
			title: "Dashboard",
			url: "/app",
			icon: IconDashboard,
		},
		{
			title: "Team",
			url: "/app/users",
			icon: IconUsers,
		},
		{
			title: "Licenses",
			url: "/app/licenses",
			icon: IconLicense,
		},
		{
			title: "Devices",
			url: "/app/devices",
			icon: IconDeviceDesktop,
		},
		{
			title: "Invitations",
			url: "/app/invitations",
			icon: IconMail,
		},
	],
	navClouds: [
		{
			title: "Capture",
			icon: IconCamera,
			isActive: true,
			url: "#",
			items: [
				{
					title: "Active Proposals",
					url: "#",
				},
				{
					title: "Archived",
					url: "#",
				},
			],
		},
		{
			title: "Proposal",
			icon: IconFileDescription,
			url: "#",
			items: [
				{
					title: "Active Proposals",
					url: "#",
				},
				{
					title: "Archived",
					url: "#",
				},
			],
		},
		{
			title: "Prompts",
			icon: IconFileAi,
			url: "#",
			items: [
				{
					title: "Active Proposals",
					url: "#",
				},
				{
					title: "Archived",
					url: "#",
				},
			],
		},
	],
	navSecondary: [
		{
			title: "Settings",
			url: "/app/settings",
			icon: IconSettings,
		},
		{
			title: "Get Help",
			url: "#",
			icon: IconHelp,
		},
		{
			title: "Search",
			url: "#",
			icon: IconSearch,
		},
	],
	documents: [
		{
			name: "Events",
			url: "/app/events",
			icon: IconTimelineEvent,
		},
		{
			name: "Payments",
			url: "/app/payments",
			icon: IconCreditCard,
		},
		{
			name: "Word Assistant",
			url: "#",
			icon: IconFileWord,
		},
	],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
	return (
		<Sidebar collapsible="offcanvas" {...props}>
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton
							asChild
							className="data-[slot=sidebar-menu-button]:!p-1.5"
						>
							<a href="/">
								<img
									src="/Snapback-logo.png"
									alt="Snapback"
									className="h-5 w-5"
								/>
								{/* <IconInnerShadowTop className="!size-5" /> */}
								<span className="font-semibold text-base">Snapback</span>
							</a>
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent>
				<NavMain items={data.navMain} />
				<NavDocuments items={data.documents} />
				<NavSecondary items={data.navSecondary} className="mt-auto" />
			</SidebarContent>
			<SidebarFooter>
				<NavUser />
			</SidebarFooter>
		</Sidebar>
	);
}
