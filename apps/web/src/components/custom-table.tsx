import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	KeyboardSensor,
	MouseSensor,
	TouchSensor,
	type UniqueIdentifier,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
	arrayMove,
	SortableContext,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
	IconChevronDown,
	IconChevronLeft,
	IconChevronRight,
	IconChevronsLeft,
	IconChevronsRight,
	IconGripVertical,
	IconLayoutColumns,
	IconPlus,
	IconSearch,
} from "@tabler/icons-react";
import {
	type ColumnDef,
	type ColumnFiltersState,
	flexRender,
	getCoreRowModel,
	getFacetedRowModel,
	getFacetedUniqueValues,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	type Row,
	type SortingState,
	useReactTable,
	type VisibilityState,
} from "@tanstack/react-table";
import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function DragHandle({ id }: { id: UniqueIdentifier }) {
	const { attributes, listeners } = useSortable({ id });
	return (
		<Button
			{...attributes}
			{...listeners}
			variant="ghost"
			size="icon"
			className="size-7 text-muted-foreground hover:bg-transparent"
		>
			<IconGripVertical className="size-3 text-muted-foreground" />
			<span className="sr-only">Drag to reorder</span>
		</Button>
	);
}

function DraggableRow<TData>({
	row,
	onRowClick,
}: {
	row: Row<TData>;
	onRowClick?: (row: Row<TData>) => void;
}) {
	const { transform, transition, setNodeRef, isDragging } = useSortable({
		id: row.id,
	});

	return (
		<TableRow
			data-state={row.getIsSelected() && "selected"}
			data-dragging={isDragging}
			ref={setNodeRef}
			className="relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80"
			style={{
				transform: CSS.Transform.toString(transform),
				transition: transition,
			}}
			onClick={() => onRowClick?.(row)}
		>
			{row.getVisibleCells().map((cell) => (
				<TableCell key={cell.id}>
					{flexRender(cell.column.columnDef.cell, cell.getContext())}
				</TableCell>
			))}
		</TableRow>
	);
}

export interface TabConfig {
	value: string;
	label: string;
	badge?: number;
}

export interface CustomTableProps<TData extends { id: UniqueIdentifier }> {
	data: TData[];
	columns: ColumnDef<TData, any>[];
	renderRowDetail?: (row: Row<TData>, close: () => void) => React.ReactNode;
	title?: string;
	showColumnCustomization?: boolean;
	showAddButton?: boolean;
	addButtonText?: string;
	onAddClick?: () => void;
	// Search functionality
	showSearch?: boolean;
	searchPlaceholder?: string;
	searchColumn?: string;
	// Tabs functionality
	tabs?: TabConfig[];
	defaultTab?: string;
	onTabChange?: (value: string) => void;
	// Tab content renderer
	renderTabContent?: (tabValue: string) => React.ReactNode;
}

export function CustomTable<TData extends { id: UniqueIdentifier }>({
	data: initialData,
	columns,
	renderRowDetail,
	title = "Data",
	showColumnCustomization = true,
	showAddButton = true,
	addButtonText = "Add Item",
	onAddClick,
	// Search props
	showSearch = false,
	searchPlaceholder = "Search...",
	searchColumn,
	// Tabs props
	tabs,
	defaultTab,
	onTabChange,
	renderTabContent,
}: CustomTableProps<TData>) {
	const [data, setData] = React.useState(() => initialData);
	const [rowSelection, setRowSelection] = React.useState({});
	const [columnVisibility, setColumnVisibility] =
		React.useState<VisibilityState>({});
	const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
		[],
	);
	const [sorting, setSorting] = React.useState<SortingState>([]);
	const [pagination, setPagination] = React.useState({
		pageIndex: 0,
		pageSize: 10,
	});
	const [activeRow, setActiveRow] = React.useState<Row<TData> | null>(null);
	const [searchValue, setSearchValue] = React.useState("");
	const [activeTab, setActiveTab] = React.useState(
		defaultTab || tabs?.[0]?.value || "main",
	);
	const sortableId = React.useId();
	const rowsPerPageId = React.useId();
	const searchId = React.useId();
	const sensors = useSensors(
		useSensor(MouseSensor, {}),
		useSensor(TouchSensor, {}),
		useSensor(KeyboardSensor, {}),
	);

	const dataIds = React.useMemo<UniqueIdentifier[]>(
		() => data?.map((row) => row.id) || [],
		[data],
	);

	const table = useReactTable({
		data,
		columns,
		state: {
			sorting,
			columnVisibility,
			rowSelection,
			columnFilters,
			pagination,
		},
		getRowId: (row) => row.id.toString(),
		enableRowSelection: true,
		onRowSelectionChange: setRowSelection,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		onColumnVisibilityChange: setColumnVisibility,
		onPaginationChange: setPagination,
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFacetedRowModel: getFacetedRowModel(),
		getFacetedUniqueValues: getFacetedUniqueValues(),
	});

	// Handle search functionality
	React.useEffect(() => {
		if (showSearch && searchColumn && searchValue) {
			table.getColumn(searchColumn)?.setFilterValue(searchValue);
		} else if (showSearch && searchColumn) {
			table.getColumn(searchColumn)?.setFilterValue("");
		}
	}, [searchValue, searchColumn, showSearch, table]);

	// Handle tab changes
	const handleTabChange = (value: string) => {
		setActiveTab(value);
		onTabChange?.(value);
	};

	function handleDragEnd(event: DragEndEvent) {
		const { active, over } = event;
		if (active && over && active.id !== over.id) {
			setData((data) => {
				const oldIndex = dataIds.indexOf(active.id);
				const newIndex = dataIds.indexOf(over.id);
				return arrayMove(data, oldIndex, newIndex);
			});
		}
	}

	// Render the table content
	const renderTableContent = () => (
		<>
			{/* Table Container */}
			<div className="relative flex flex-col gap-4 overflow-auto">
				<div className="overflow-hidden border rounded-lg">
					<DndContext
						collisionDetection={closestCenter}
						modifiers={[restrictToVerticalAxis]}
						onDragEnd={handleDragEnd}
						sensors={sensors}
						id={sortableId}
					>
						<Table>
							<TableHeader className="sticky top-0 z-10 bg-muted">
								{table.getHeaderGroups().map((headerGroup) => (
									<TableRow key={headerGroup.id}>
										{headerGroup.headers.map((header) => {
											return (
												<TableHead key={header.id} colSpan={header.colSpan}>
													{header.isPlaceholder
														? null
														: flexRender(
																header.column.columnDef.header,
																header.getContext(),
															)}
												</TableHead>
											);
										})}
									</TableRow>
								))}
							</TableHeader>
							<TableBody className="**:data-[slot=table-cell]:first:w-8">
								{table.getRowModel().rows?.length ? (
									<SortableContext
										items={dataIds}
										strategy={verticalListSortingStrategy}
									>
										{table.getRowModel().rows.map((row) => (
											<DraggableRow
												key={row.id}
												row={row}
												onRowClick={setActiveRow}
											/>
										))}
									</SortableContext>
								) : (
									<TableRow>
										<TableCell
											colSpan={columns.length}
											className="h-24 text-center"
										>
											<IconSearch className="mx-auto mb-4 size-6 text-muted-foreground" />
											No results.
										</TableCell>
									</TableRow>
								)}
							</TableBody>
						</Table>
					</DndContext>
				</div>

				{/* Pagination */}
				<div className="flex items-center justify-between px-4">
					<div className="flex-1 hidden text-sm text-muted-foreground lg:flex">
						{table.getFilteredSelectedRowModel().rows.length} of{" "}
						{table.getFilteredRowModel().rows.length} row(s) selected.
					</div>
					<div className="flex items-center w-full gap-8 lg:w-fit">
						<div className="items-center hidden gap-2 lg:flex">
							<Label htmlFor={rowsPerPageId} className="text-sm font-medium">
								Rows per page
							</Label>
							<Select
								value={`${table.getState().pagination.pageSize}`}
								onValueChange={(value) => {
									table.setPageSize(Number(value));
								}}
							>
								<SelectTrigger size="sm" className="w-20" id={rowsPerPageId}>
									<SelectValue
										placeholder={table.getState().pagination.pageSize}
									/>
								</SelectTrigger>
								<SelectContent side="top">
									{[10, 20, 30, 40, 50].map((pageSize) => (
										<SelectItem key={pageSize} value={`${pageSize}`}>
											{pageSize}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
						<div className="flex items-center justify-center text-sm font-medium w-fit">
							Page {table.getState().pagination.pageIndex + 1} of{" "}
							{table.getPageCount()}
						</div>
						<div className="flex items-center gap-2 ml-auto lg:ml-0">
							<Button
								variant="outline"
								className="hidden w-8 h-8 p-0 lg:flex"
								onClick={() => table.setPageIndex(0)}
								disabled={!table.getCanPreviousPage()}
							>
								<span className="sr-only">Go to first page</span>
								<IconChevronsLeft />
							</Button>
							<Button
								variant="outline"
								className="size-8"
								size="icon"
								onClick={() => table.previousPage()}
								disabled={!table.getCanPreviousPage()}
							>
								<span className="sr-only">Go to previous page</span>
								<IconChevronLeft />
							</Button>
							<Button
								variant="outline"
								className="size-8"
								size="icon"
								onClick={() => table.nextPage()}
								disabled={!table.getCanNextPage()}
							>
								<span className="sr-only">Go to next page</span>
								<IconChevronRight />
							</Button>
							<Button
								variant="outline"
								className="hidden size-8 lg:flex"
								size="icon"
								onClick={() => table.setPageIndex(table.getPageCount() - 1)}
								disabled={!table.getCanNextPage()}
							>
								<span className="sr-only">Go to last page</span>
								<IconChevronsRight />
							</Button>
						</div>
					</div>
				</div>
			</div>
		</>
	);

	// If tabs are provided, wrap in Tabs component
	if (tabs && tabs.length > 0) {
		return (
			<Tabs
				value={activeTab}
				onValueChange={handleTabChange}
				className="flex-col justify-start w-full gap-6"
			>
				{/* Header Controls */}
				<div className="flex items-center justify-between px-4 lg:px-6">
					{/* Mobile tab selector */}
					<Label htmlFor="view-selector" className="sr-only">
						View
					</Label>
					<Select value={activeTab} onValueChange={handleTabChange}>
						{/** biome-ignore lint/correctness/useUniqueElementIds: unique id */}
						<SelectTrigger
							className="flex @4xl/main:hidden w-fit"
							size="sm"
							id="view-selector"
						>
							<SelectValue placeholder="Select a view" />
						</SelectTrigger>
						<SelectContent>
							{tabs.map((tab) => (
								<SelectItem key={tab.value} value={tab.value}>
									{tab.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>

					{/* Desktop tabs */}
					<TabsList className="@4xl/main:flex hidden **:data-[slot=badge]:size-5 **:data-[slot=badge]:rounded-full **:data-[slot=badge]:bg-muted-foreground/30 **:data-[slot=badge]:px-1">
						{tabs.map((tab) => (
							<TabsTrigger key={tab.value} value={tab.value}>
								{tab.label}
								{tab.badge && <Badge variant="secondary">{tab.badge}</Badge>}
							</TabsTrigger>
						))}
					</TabsList>

					<div className="flex items-center gap-2">
						{/* Search Input */}
						{showSearch && (
							<div className="relative">
								<IconSearch className="absolute -translate-y-1/2 top-1/2 left-2 size-4 text-muted-foreground" />
								<Input
									id={searchId}
									placeholder={searchPlaceholder}
									value={searchValue}
									onChange={(e) => setSearchValue(e.target.value)}
									className="w-64 pl-8"
								/>
							</div>
						)}

						{showColumnCustomization && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button variant="outline" size="sm">
										<IconLayoutColumns />
										<span className="hidden lg:inline">Customize Columns</span>
										<span className="lg:hidden">Columns</span>
										<IconChevronDown />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end" className="w-56">
									{table
										.getAllColumns()
										.filter(
											(column) =>
												typeof column.accessorFn !== "undefined" &&
												column.getCanHide(),
										)
										.map((column) => {
											return (
												<DropdownMenuCheckboxItem
													key={column.id}
													className="capitalize"
													checked={column.getIsVisible()}
													onCheckedChange={(value) =>
														column.toggleVisibility(!!value)
													}
												>
													{column.id}
												</DropdownMenuCheckboxItem>
											);
										})}
								</DropdownMenuContent>
							</DropdownMenu>
						)}
						{showAddButton && (
							<Button variant="outline" size="sm" onClick={onAddClick}>
								<IconPlus />
								<span className="hidden lg:inline">{addButtonText}</span>
							</Button>
						)}
					</div>
				</div>

				{/* Tab Contents */}
				{tabs.map((tab) => (
					<TabsContent
						key={tab.value}
						value={tab.value}
						className="relative flex flex-col gap-4 px-4 overflow-auto lg:px-6"
					>
						{renderTabContent
							? renderTabContent(tab.value)
							: renderTableContent()}
					</TabsContent>
				))}

				{/* Detail slot (drawer/modal/etc.) */}
				{activeRow && renderRowDetail?.(activeRow, () => setActiveRow(null))}
			</Tabs>
		);
	}

	// Default layout without tabs
	return (
		<div className="flex flex-col justify-start w-full gap-6">
			{/* Header Controls */}
			<div className="flex items-center justify-between px-4">
				<div className="flex items-center gap-2">
					<h2 className="text-lg font-semibold">{title}</h2>
				</div>
				<div className="flex items-center gap-2">
					{/* Search Input */}
					{showSearch && (
						<div className="relative">
							<IconSearch className="absolute -translate-y-1/2 top-1/2 left-2 size-4 text-muted-foreground" />
							<Input
								id={searchId}
								placeholder={searchPlaceholder}
								value={searchValue}
								onChange={(e) => setSearchValue(e.target.value)}
								className="w-64 pl-8"
							/>
						</div>
					)}

					{showColumnCustomization && (
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="outline" size="sm">
									<IconLayoutColumns />
									<span className="hidden lg:inline">Customize Columns</span>
									<span className="lg:hidden">Columns</span>
									<IconChevronDown />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end" className="w-56">
								{table
									.getAllColumns()
									.filter(
										(column) =>
											typeof column.accessorFn !== "undefined" &&
											column.getCanHide(),
									)
									.map((column) => {
										return (
											<DropdownMenuCheckboxItem
												key={column.id}
												className="capitalize"
												checked={column.getIsVisible()}
												onCheckedChange={(value) =>
													column.toggleVisibility(!!value)
												}
											>
												{column.id}
											</DropdownMenuCheckboxItem>
										);
									})}
							</DropdownMenuContent>
						</DropdownMenu>
					)}
					{showAddButton && (
						<Button variant="outline" size="sm" onClick={onAddClick}>
							<IconPlus />
							<span className="hidden lg:inline">{addButtonText}</span>
						</Button>
					)}
				</div>
			</div>

			{renderTableContent()}

			{/* Detail slot (drawer/modal/etc.) */}
			{activeRow && renderRowDetail?.(activeRow, () => setActiveRow(null))}
		</div>
	);
}
