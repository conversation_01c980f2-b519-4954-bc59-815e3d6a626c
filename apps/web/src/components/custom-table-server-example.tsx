import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { CustomTable, DragHandle, type PaginationParams, type ServerPaginatedData } from "./custom-table";
import * as React from "react";

// Example data type
interface ExampleData {
	id: number;
	name: string;
	status: "active" | "inactive";
	email: string;
	role: string;
}

// Mock server data
const mockServerData: ExampleData[] = Array.from({ length: 100 }, (_, i) => ({
	id: i + 1,
	name: `User ${i + 1}`,
	status: i % 3 === 0 ? "inactive" : "active",
	email: `user${i + 1}@example.com`,
	role: ["Admin", "User", "Editor"][i % 3],
}));

// Mock server API function
const fetchServerData = async (params: PaginationParams): Promise<ServerPaginatedData<ExampleData>> => {
	// Simulate API delay
	await new Promise(resolve => setTimeout(resolve, 1000));
	
	let filteredData = [...mockServerData];
	
	// Apply search filter
	if (params.search) {
		filteredData = filteredData.filter(item => 
			item.name.toLowerCase().includes(params.search!.toLowerCase()) ||
			item.email.toLowerCase().includes(params.search!.toLowerCase())
		);
	}
	
	// Apply sorting
	if (params.sortBy) {
		filteredData.sort((a, b) => {
			const aVal = a[params.sortBy as keyof ExampleData];
			const bVal = b[params.sortBy as keyof ExampleData];
			const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
			return params.sortOrder === "desc" ? -comparison : comparison;
		});
	}
	
	const page = params.page || 1;
	const limit = params.limit || 10;
	const startIndex = (page - 1) * limit;
	const endIndex = startIndex + limit;
	const items = filteredData.slice(startIndex, endIndex);
	
	const totalCount = filteredData.length;
	const totalPages = Math.ceil(totalCount / limit);
	
	return {
		items,
		meta: {
			page,
			limit,
			totalCount,
			totalPages,
			nextPage: page < totalPages ? page + 1 : undefined,
			previousPage: page > 1 ? page - 1 : undefined,
		},
	};
};

// Example columns with the same styling as data-table.tsx
const exampleColumns: ColumnDef<ExampleData>[] = [
	{
		id: "drag",
		header: () => null,
		cell: ({ row }) => <DragHandle id={row.original.id} />,
	},
	{
		id: "select",
		header: ({ table }) => (
			<div className="flex items-center justify-center">
				<Checkbox
					checked={
						table.getIsAllPageRowsSelected() ||
						(table.getIsSomePageRowsSelected() && "indeterminate")
					}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			</div>
		),
		cell: ({ row }) => (
			<div className="flex items-center justify-center">
				<Checkbox
					checked={row.getIsSelected()}
					onCheckedChange={(value) => row.toggleSelected(!!value)}
					aria-label="Select row"
				/>
			</div>
		),
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "name",
		header: "Name",
		cell: ({ row }) => (
			<Button variant="link" className="w-fit px-0 text-left text-foreground">
				{row.original.name}
			</Button>
		),
		enableHiding: false,
	},
	{
		accessorKey: "status",
		header: "Status",
		cell: ({ row }) => (
			<Badge 
				variant="outline" 
				className={`px-1.5 ${
					row.original.status === "active" 
						? "text-green-600 border-green-200" 
						: "text-gray-600 border-gray-200"
				}`}
			>
				{row.original.status}
			</Badge>
		),
	},
	{
		accessorKey: "email",
		header: "Email",
		cell: ({ row }) => (
			<span className="text-muted-foreground">{row.original.email}</span>
		),
	},
	{
		accessorKey: "role",
		header: "Role",
		cell: ({ row }) => (
			<Badge variant="outline" className="px-1.5 text-muted-foreground">
				{row.original.role}
			</Badge>
		),
	},
];

export function CustomTableServerExample() {
	const [serverData, setServerData] = React.useState<ServerPaginatedData<ExampleData> | null>(null);
	const [loading, setLoading] = React.useState(false);
	const [params, setParams] = React.useState<PaginationParams>({
		page: 1,
		limit: 10,
	});

	const handlePaginationChange = React.useCallback(async (newParams: PaginationParams) => {
		setLoading(true);
		setParams(newParams);
		try {
			const data = await fetchServerData(newParams);
			setServerData(data);
		} catch (error) {
			console.error("Failed to fetch data:", error);
		} finally {
			setLoading(false);
		}
	}, []);

	// Initial data load
	React.useEffect(() => {
		handlePaginationChange(params);
	}, []); // Only run on mount

	if (!serverData) {
		return <div>Loading initial data...</div>;
	}

	return (
		<div className="container mx-auto py-8">
			<CustomTable
				data={serverData}
				columns={exampleColumns}
				title="Server-Side Paginated Users"
				addButtonText="Add User"
				onAddClick={() => console.log("Add user clicked")}
				// Server-side configuration
				isServerSide={true}
				loading={loading}
				onPaginationChange={handlePaginationChange}
				initialParams={params}
				// Search functionality
				showSearch={true}
				searchPlaceholder="Search users..."
				// Tabs functionality
				tabs={[
					{ value: "all", label: "All Users", badge: serverData.meta.totalCount },
					{ value: "active", label: "Active" },
					{ value: "inactive", label: "Inactive" },
				]}
				defaultTab="all"
				onTabChange={(tab) => {
					console.log("Tab changed to:", tab);
					// You could filter server data based on tab here
					// handlePaginationChange({ ...params, filters: { status: tab === "all" ? undefined : tab } });
				}}
				renderRowDetail={(row, close) => (
					<div className="p-4">
						<h3 className="mb-2 text-lg font-semibold">User Details</h3>
						<p><strong>Name:</strong> {row.original.name}</p>
						<p><strong>Email:</strong> {row.original.email}</p>
						<p><strong>Role:</strong> {row.original.role}</p>
						<p><strong>Status:</strong> {row.original.status}</p>
						<Button onClick={close} className="mt-4">Close</Button>
					</div>
				)}
			/>
		</div>
	);
}
