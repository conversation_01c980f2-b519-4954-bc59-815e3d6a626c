import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { CustomTable, DragHandle } from "./custom-table";

// Example data type
interface ExampleData {
	id: number;
	name: string;
	status: "active" | "inactive";
	email: string;
	role: string;
}

// Example data
const exampleData: ExampleData[] = [
	{
		id: 1,
		name: "<PERSON>",
		status: "active",
		email: "<EMAIL>",
		role: "Admin",
	},
	{
		id: 2,
		name: "<PERSON>",
		status: "inactive",
		email: "<EMAIL>",
		role: "User",
	},
	{
		id: 3,
		name: "<PERSON>",
		status: "active",
		email: "<EMAIL>",
		role: "Editor",
	},
];

// Example columns with the same styling as data-table.tsx
const exampleColumns: ColumnDef<ExampleData>[] = [
	{
		id: "drag",
		header: () => null,
		cell: ({ row }) => <DragHandle id={row.original.id} />,
	},
	{
		id: "select",
		header: ({ table }) => (
			<div className="flex items-center justify-center">
				<Checkbox
					checked={
						table.getIsAllPageRowsSelected() ||
						(table.getIsSomePageRowsSelected() && "indeterminate")
					}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			</div>
		),
		cell: ({ row }) => (
			<div className="flex items-center justify-center">
				<Checkbox
					checked={row.getIsSelected()}
					onCheckedChange={(value) => row.toggleSelected(!!value)}
					aria-label="Select row"
				/>
			</div>
		),
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "name",
		header: "Name",
		cell: ({ row }) => (
			<Button variant="link" className="w-fit px-0 text-left text-foreground">
				{row.original.name}
			</Button>
		),
		enableHiding: false,
	},
	{
		accessorKey: "status",
		header: "Status",
		cell: ({ row }) => (
			<Badge 
				variant="outline" 
				className={`px-1.5 ${
					row.original.status === "active" 
						? "text-green-600 border-green-200" 
						: "text-gray-600 border-gray-200"
				}`}
			>
				{row.original.status}
			</Badge>
		),
	},
	{
		accessorKey: "email",
		header: "Email",
		cell: ({ row }) => (
			<span className="text-muted-foreground">{row.original.email}</span>
		),
	},
	{
		accessorKey: "role",
		header: "Role",
		cell: ({ row }) => (
			<Badge variant="outline" className="px-1.5 text-muted-foreground">
				{row.original.role}
			</Badge>
		),
	},
];

export function CustomTableExample() {
	return (
		<div className="container mx-auto py-8">
			<CustomTable
				data={exampleData}
				columns={exampleColumns}
				title="Users"
				addButtonText="Add User"
				onAddClick={() => console.log("Add user clicked")}
				renderRowDetail={(row, close) => (
					<div className="p-4">
						<h3 className="text-lg font-semibold mb-2">User Details</h3>
						<p><strong>Name:</strong> {row.original.name}</p>
						<p><strong>Email:</strong> {row.original.email}</p>
						<p><strong>Role:</strong> {row.original.role}</p>
						<p><strong>Status:</strong> {row.original.status}</p>
						<Button onClick={close} className="mt-4">Close</Button>
					</div>
				)}
			/>
		</div>
	);
}
