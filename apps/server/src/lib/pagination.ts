import { z } from "zod";
import { metaSchema } from "@/types";

type PaginationInput = {
	page?: number;
	limit?: number;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	filters?: Record<string, any>;
	search?: string;
};

export function buildPaginationParams(input: PaginationInput) {
	const {
		page = 1,
		limit = 10,
		sortBy,
		sortOrder = "asc",
		filters,
		search,
	} = input;

	const skip = (page - 1) * limit;
	const take = limit;

	const orderBy = sortBy ? { [sortBy]: sortOrder } : undefined;

	return { skip, take, orderBy, filters, search, page };
}

export async function buildListResponse<
	TModel extends { findMany: any; count: any },
	TWhere extends Record<string, any>,
	TInclude extends Record<string, any>,
>(model: TModel, input: PaginationInput, where?: TWhere, include?: TInclude) {
	const { skip, take, orderBy, page } = buildPaginationParams(input);

	const [totalCount, items] = await Promise.all([
		model.count({ where }),
		model.findMany({ skip, take, orderBy, where, include }),
	]);

	return {
		items,
		meta: {
			totalCount,
			totalPages: Math.ceil(totalCount / take),
			page,
			limit: take,
			nextPage: totalCount > skip + take ? page + 1 : undefined,
			previousPage: skip > 0 ? page - 1 : undefined,
		},
	};
}

export function createListOutputSchema<T extends z.ZodTypeAny>(itemSchema: T) {
	return z.object({
		items: z.array(itemSchema),
		meta: metaSchema,
	});
}
