import { LicenseStatus, LicenseType } from "prisma/generated/enums";
import {
	LicenseInputSchema,
	LicenseModelSchema,
} from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const LicenseListSchema = createListOutputSchema(LicenseModelSchema);
export const licenses = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.license.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(LicenseListSchema)
		.handler(async ({ input }) => {
			const { search, filters } = input;

			let where: any = {};

			if (search) {
				where = {
					...where,
					OR: [
						{ customerEmail: { contains: search } },
						{ customerName: { contains: search } },
					],
					...filters,
				};
			}

			return await buildListResponse(prisma.license, input, where);
		}),

	get: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.license.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.input(LicenseInputSchema)
		.handler(async ({ input }) => {
			function generateLicenseKey() {
				const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
				let result = "";
				for (let i = 0; i < 24; i++) {
					result += characters.charAt(
						Math.floor(Math.random() * characters.length),
					);
				}
				return result;
			}
			const licenseKey = generateLicenseKey();

			return await prisma.license.create({
				data: {
					customerEmail: input.customerEmail,
					customerName: input.customerName,
					licenseType: input.licenseType,
					licenseKey: licenseKey,
				},
			});
		}),

	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				licenseType: z.enum(LicenseType).optional(),
				status: z.enum(LicenseStatus).optional(),
				maxDevices: z.number().int().min(1).optional(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.license.update({
				where: {
					id: input.id,
				},
				data: {
					licenseType: input.licenseType,
					status: input.status,
					maxDevices: input.maxDevices,
				},
			});
		}),

	delete: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.license.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
