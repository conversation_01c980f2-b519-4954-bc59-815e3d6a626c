import type { RouterClient } from "@orpc/server";

import { protectedProcedure, publicProcedure } from "@/lib/orpc";
import { accounts } from "./accounts";
import { devices } from "./devices";
import { events } from "./events";
import { expansions } from "./expansions";
import { invitations } from "./invitations";
import { licenses } from "./licenses";
import { logs } from "./logs";
import { messages } from "./messages";
import { payments } from "./payments";
import { rateLimits } from "./rate-limits";
import { refunds } from "./refunds";
import { sessions } from "./sessions";
import { tickets } from "./tickets";
import { users } from "./users";

export const appRouter = {
	healthCheck: publicProcedure.handler(() => {
		return "OK";
	}),
	devices,
	users,
	licenses,
	payments,
	events,
	logs,
	expansions,
	refunds,
	tickets,
	messages,
	rateLimits,
	accounts,
	sessions,
	invitations,
};
export type AppRouter = typeof appRouter;
export type AppRouterClient = RouterClient<typeof appRouter>;
