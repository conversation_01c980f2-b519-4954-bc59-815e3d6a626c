import { RefundStatus } from "prisma/generated/enums";
import {
	RefundRequestInputSchema,
	RefundRequestModelSchema,
} from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const RefundRequestListSchema = createListOutputSchema(
	RefundRequestModelSchema,
);
export const refunds = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.refundRequest.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(RefundRequestListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.refundRequest, input);
		}),

	get: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.input(RefundRequestInputSchema)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.create({
				data: {
					licenseId: input.licenseId,
					requestedBy: input.requestedBy,
					reason: input.reason,
				},
			});
		}),

	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				status: z.enum(RefundStatus).optional(),
				approvedAmount: z.number().int().min(1).optional(),
				stripeRefundIds: z.array(z.string()).optional(),
				adminNotes: z.string().optional(),
				processedBy: z.string().optional(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
					approvedAmount: input.approvedAmount,
					stripeRefundIds: input.stripeRefundIds,
					adminNotes: input.adminNotes,
					processedBy: input.processedBy,
				},
			});
		}),

	delete: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
