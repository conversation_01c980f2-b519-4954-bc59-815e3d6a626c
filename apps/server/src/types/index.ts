import { z } from "zod";

// export const urlSchema = z.url("Valid success URL is required");
// export const passwordSchema = z
// 	.string()
// 	.min(8, "Password must be at least 8 characters")
// 	.max(100, "Password must be at most 100 characters");

// export const isoDateSchema = z.iso.datetime();
// export const emailSchema = z
// 	.email("Invalid email address")
// 	.max(254, "Email must be at most 254 characters")
// 	.transform((val) => val.toLowerCase().trim());

// export const licenseKeySchema = z
// 	.string()
// 	.length(24, "License key must be exactly 24 characters")
// 	.regex(
// 		/^[A-Z0-9]{24}$/,
// 		"License key must contain only uppercase letters and numbers",
// 	);

// export const deviceIdSchema = z
// 	.string()
// 	.min(32, "Device ID must be at least 32 characters")
// 	.max(128, "Device ID must be at most 128 characters");

// export const deviceMetadataSchema = z.object({
// 	deviceName: z.string().optional(),
// 	deviceType: z.string().optional(),
// 	deviceModel: z.string().optional(),
// 	operatingSystem: z.string().optional(),
// 	architecture: z.string().optional(),
// 	screenResolution: z.string().optional(),
// 	totalMemory: z.string().optional(),
// 	userNickname: z.string().optional(),
// 	location: z.string().optional(),
// 	notes: z.string().optional(),
// });

/**
 * Standardized pagination schema used across all list endpoints
 *
 * Features:
 * - page: Page number (1-based, default: 1)
 * - limit: Items per page (1-100, default: 20)
 * - search: General search term (optional)
 * - sortBy: Field to sort by (optional)
 * - sortOrder: Sort direction "asc" or "desc" (default: "desc")
 * - filters: Flexible object for specific filters (optional)
 *
 * Example usage:
 * - Basic: ?page=2&limit=50
 * - With search: ?search=john&sortBy=createdAt&sortOrder=asc
 * - With filters: ?filters[status]=ACTIVE&filters[licenseType]=PRO
 */

export const paginationSchema = z.object({
	page: z.number().int().min(1).default(1),
	limit: z.number().int().min(1).max(100).default(20),
	search: z.string().optional(),
	sortBy: z.string().optional(),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
	filters: z.record(z.string(), z.any()).optional(), // Flexible filters object
});

export const metaSchema = z.object({
	totalCount: z.number(),
	totalPages: z.number(),
	page: z.number(),
	limit: z.number(),
	nextPage: z.number().optional(),
	previousPage: z.number().optional(),
});
