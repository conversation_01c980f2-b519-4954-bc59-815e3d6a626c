FROM oven/bun:1.2.21-alpine AS base

FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat
# Set working directory
WORKDIR /app
# Install turbo globally
RUN bun add -g turbo@^2
COPY . .

# Generate a partial monorepo with a pruned lockfile for a target workspace.
# Assuming "server" is the name entered in the project's package.json: { name: "server" }
RUN turbo prune server --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

# First install the dependencies (as they change less often)
COPY --from=builder /app/out/json/ .
RUN bun install --frozen-lockfile

# Build the project
COPY --from=builder /app/out/full/ .
RUN bun run turbo run build --filter=server

FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 server
USER server

# Copy the built application
COPY --from=installer --chown=server:nodejs /app/apps/server/dist ./dist
COPY --from=installer --chown=server:nodejs /app/apps/server/package.json ./package.json
COPY --from=installer --chown=server:nodejs /app/apps/server/prisma ./prisma

# Install production dependencies
RUN bun install --production --frozen-lockfile

EXPOSE 3000

# Set environment variables
ENV NODE_ENV=production

CMD ["bun", "run", "dist/index.js"]
