# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
bun.lockb

# Production builds
dist
build
*.tgz

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# TypeScript
*.tsbuildinfo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Git
.git
.gitignore

# Docker
Dockerfile*
.dockerignore

# Testing
coverage
*.lcov

# Temporary folders
tmp/
temp/

# Database files (keep schema and migrations)
*.db
*.sqlite
*.sqlite3

# Prisma generated files (will be regenerated)
prisma/generated/

# Development database
dev.db
