FROM node:18-alpine AS base

FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat
# Set working directory
WORKDIR /app
# Replace with the major version installed in your repository
RUN yarn global add turbo@^2
# Install Expo CLI globally
RUN yarn global add @expo/cli@latest
COPY . .

# Generate a partial monorepo with a pruned lockfile for a target workspace.
# Assuming "native" is the name entered in the project's package.json: { name: "native" }
RUN turbo prune native --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install Expo CLI globally
RUN yarn global add @expo/cli@latest

# First install the dependencies (as they change less often)
COPY --from=builder /app/out/json/ .
RUN yarn install --frozen-lockfile

# Build the project for web
COPY --from=builder /app/out/full/ .
WORKDIR /app/apps/native
RUN expo export --platform web --output-dir dist

FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 expo
USER expo

# Copy the built web application
COPY --from=installer --chown=expo:nodejs /app/apps/native/dist ./dist
COPY --from=installer --chown=expo:nodejs /app/apps/native/package.json ./package.json

# Install a simple HTTP server to serve the static files
RUN yarn global add serve

EXPOSE 3000

CMD ["serve", "-s", "dist", "-l", "3000"]
