# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Expo
.expo/
.expo-shared/
expo-env.d.ts

# React Native
.react-native/
android/
ios/

# Production builds
dist
build
*.tgz

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# TypeScript
*.tsbuildinfo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Testing
coverage
*.lcov

# Temporary folders
tmp/
temp/

# Metro bundler cache
.metro-cache/

# Watchman
.watchmanconfig
